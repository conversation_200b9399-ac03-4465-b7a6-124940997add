{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@tabler/icons-react": "^3.35.0", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "lucide-react": "^0.544.0", "motion": "^12.23.16", "next": "15.5.3", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.5.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.3", "typescript": "^5"}}