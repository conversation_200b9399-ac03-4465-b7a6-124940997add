type TimelineItem = {
  year: string;
  title: string;
  description?: string;
};

const defaultItems: TimelineItem[] = [
  {
    year: "2016",
    title: "Establishment",
    description: "Specialized HVAC services.",
  },
  {
    year: "2017",
    title: "Incorporation",
    description: "Registered company in India.",
  },
  {
    year: "2018",
    title: "Team Grows",
    description: "Expanded with market demand.",
  },
  {
    year: "2019",
    title: "Diversification",
    description: "Core HVAC equipment & design.",
  },
  { year: "2020", title: "Milestone", description: "Trusted partner for O&M." },
];

export function Journey({
  items = defaultItems,
  heading = "Our Journey",
  subheading = "Highlights from our growth and milestones.",
}: {
  items?: TimelineItem[];
  heading?: string;
  subheading?: string;
}) {
  return (
    <section className="w-full bg-background text-foreground">
      <div className="mx-auto max-w-5xl px-4 py-12 md:py-16">
        <header className="mb-8 text-center">
          <h2 className="text-balance text-2xl font-light md:text-3xl">
            {heading}
          </h2>
          <p className="text-pretty mt-2 text-sm text-muted-foreground md:text-base">
            {subheading}
          </p>
        </header>

        {/* Timeline */}
        <div className="relative">
          {/* Horizontal line */}
          <hr
            className="border-border absolute inset-x-0 top-6 border-t"
            aria-hidden="true"
          />

          {/* Milestones */}
          <ol
            className="relative z-10 grid grid-cols-2 items-start gap-y-10 md:grid-cols-5 md:gap-y-12"
            aria-label="Company journey timeline"
            role="list"
          >
            {items.map((item, idx) => (
              <li
                key={idx}
                role="listitem"
                className="flex flex-col items-center text-center"
              >
                {/* Dot on the line */}
                <span
                  className="bg-primary ring-background relative z-10 inline-flex h-3 w-3 rounded-full ring-2"
                  aria-hidden="true"
                />
                {/* Labels */}
                <div className="mt-4">
                  <div className="text-xs font-medium tracking-wide text-muted-foreground">
                    {item.year}
                  </div>
                  <div className="mt-1 text-sm font-semibold">{item.title}</div>
                  {item.description ? (
                    <p className="mt-1 max-w-[16rem] text-xs text-muted-foreground">
                      {item.description}
                    </p>
                  ) : null}
                </div>
              </li>
            ))}
          </ol>
        </div>
      </div>
    </section>
  );
}

export default Journey;
