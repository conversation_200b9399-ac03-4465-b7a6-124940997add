import React from 'react';

const AdvantagesContract = () => {
  const advantages = [
    {
      id: 1,
      title: "Cost Savings",
      description: "AMC services save from unexpected maintenance and repair cost. Besides this, the clients can avail the systematically & timely executed annual maintenance contract services that also renders rounds the clock maintenance & repair from qualified technicians and support staff."
    },
    {
      id: 2,
      title: "Planned Maintenance Services",
      description: "Annual maintenance contract also provides the clients with effective advantages of planning of the maintenance and repair services at regular intervals of time of the products such as air conditioners or heavy machinery."
    },
    {
      id: 3,
      title: "Genuine Spare Parts",
      description: "The annual maintenance contract enables the clients with only the genuine spare parts of the machinery or air conditioners for better performance and durability."
    },
    {
      id: 4,
      title: "24/7 Technical Support",
      description: "Round the clock maintenance & repair services from qualified technicians and support staff ensures your equipment is always running optimally."
    },
    {
      id: 5,
      title: "Extended Equipment Life",
      description: "Regular maintenance and use of genuine parts significantly extends the lifespan of your HVAC equipment and machinery."
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-br from-blue-50 to-white">
      <div className="container mx-auto px-4 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-light text-gray-800 mb-4">
            Advantages of Annual 
            <span className="text-blue-600 block mt-2">Maintainence Contract</span>
          </h2>
          <div className="w-24 h-1 bg-blue-600 mx-auto mb-6"></div>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Discover the comprehensive benefits of our annual maintenance contracts 
            that ensure optimal performance and longevity of your HVAC systems.
          </p>
        </div>

        {/* Advantages Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {advantages.map((advantage, index) => (
            <div 
              key={advantage.id}
              className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 p-6 border-l-4 border-blue-500 group"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4 group-hover:bg-blue-200 transition-colors">
                  <span className="text-blue-600 font-bold text-lg">
                    {String(index + 1).padStart(2, '0')}
                  </span>
                </div>
                <h3 className="text-xl font-semibold text-gray-800">
                  {advantage.title}
                </h3>
              </div>
              <p className="text-gray-600 leading-relaxed">
                {advantage.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default AdvantagesContract;